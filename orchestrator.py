from openai import OpenAI
import json
import math_tools
# Connect to LM Studio local API
client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")

# System instruction for Qwen
system_msg = {
    "role": "system",
    "content": "You are an assistant that can call tools when needed.The list of tools is in tools_list.json"
}

# Tool definitions for Qwen
with open('tools_list.json', 'r') as f:
    tools = json.load(f)

# Map tool names to their corresponding functions
TOOL_FUNCTIONS = {
    "add_numbers": math_tools.add_numbers,
    "subtract_numbers": math_tools.subtract_numbers,
    "multiply_numbers": math_tools.multiply_numbers,
    "divide_numbers": math_tools.divide_numbers,
    "square_number": math_tools.square_number,
    "cube_number": math_tools.cube_number,
    "square_root": math_tools.square_root,
    "cube_root": math_tools.cube_root
}


# Ask the user
user_input = input("User: ")
user_msg = {"role": "user", "content": user_input}

# Step 1: <PERSON>wen initial response (may contain tool call)
resp = client.chat.completions.create(
    model="qwen-4b",
    messages=[system_msg, user_msg],
    tools=tools
)

msg = resp.choices[0].message

# Step 2: Check if Qwen calls a tool and handle it
if msg.tool_calls:
    print(f"\n🔧 TOOL CALL DETECTED! {len(msg.tool_calls)} tool(s) will be executed.")
    tool_msgs = [system_msg, user_msg, msg]  # conversation history

    for i, call in enumerate(msg.tool_calls, 1):
        tool_name = call.function.name
        args = json.loads(call.function.arguments)

        print(f"\n📋 Tool Call #{i}:")
        print(f"   Tool Name: {tool_name}")
        print(f"   Arguments: {args}")
        print(f"   Call ID: {call.id}")

        # Execute the tool
        if tool_name in TOOL_FUNCTIONS:
            print(f"   ⚡ Executing {tool_name}...")
            try:
                # Get the function and call it with the provided arguments
                tool_function = TOOL_FUNCTIONS[tool_name]
                if tool_name in ["square_number", "cube_number", "square_root", "cube_root"]:
                    # Single argument functions
                    result = tool_function(args["a"])
                else:
                    # Two argument functions
                    result = tool_function(args["a"], args["b"])
                print(f"   ✅ Tool result: {result}")
            except Exception as e:
                print(f"   ❌ Error executing {tool_name}: {str(e)}")
                result = f"Error executing {tool_name}: {str(e)}"
        else:
            print(f"   ❌ Unknown tool: {tool_name}")
            result = f"Error: Unknown tool '{tool_name}'"

        # Send result back to Qwen
        tool_result_msg = {
            "role": "tool",
            "tool_call_id": call.id,
            "content": json.dumps({"result": result})
        }
        tool_msgs.append(tool_result_msg)

    # Step 3: Qwen final answer using tool output
    print(f"\n🤖 Getting final response from Qwen with tool results...")
    final_resp = client.chat.completions.create(
        model="qwen-4b",
        messages=tool_msgs
    )

    print("\nAssistant:", final_resp.choices[0].message.content)
else:
    # No tool call — just output Qwen's text
    print("\n💬 No tool calls detected - direct response from Qwen.")
    print("Assistant:", msg.content)
