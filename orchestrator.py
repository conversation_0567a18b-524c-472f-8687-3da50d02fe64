from openai import OpenAI
import json
import math_tools
# Connect to LM Studio local API
client = OpenAI(base_url="http://localhost:1234/v1", api_key="lm-studio")

# System instruction for Qwen
system_msg = {
    "role": "system",
    "content": "You are an assistant that can call tools when needed.The list of tools is in tools_list.json"
}

# Tool definitions for Qwen
tools = tools_list.json


# Ask the user
user_input = input("User: ")
user_msg = {"role": "user", "content": user_input}

# Step 1: Qwen initial response (may contain tool call)
resp = client.chat.completions.create(
    model="qwen-4b",
    messages=[system_msg, user_msg],
    tools=tools
)

msg = resp.choices[0].message

# Step 2: Check if <PERSON><PERSON> calls a tool and handle it
if msg.tool_calls:
    print(f"\n🔧 TOOL CALL DETECTED! {len(msg.tool_calls)} tool(s) will be executed.")
    tool_msgs = [system_msg, user_msg, msg]  # conversation history

    for i, call in enumerate(msg.tool_calls, 1):
        tool_name = call.function.name
        args = json.loads(call.function.arguments)

        print(f"\n📋 Tool Call #{i}:")
        print(f"   Tool Name: {tool_name}")
        print(f"   Arguments: {args}")
        print(f"   Call ID: {call.id}")

        # Execute the tool
        if tool_name == "add_numbers":
            print(f"   ⚡ Executing {tool_name}...")
            result = math_tools.add_numbers(args["a"], args["b"])
            print(f"   ✅ Tool result: {result}")
        else:
            print(f"   ❌ Unknown tool: {tool_name}")
            result = f"Error: Unknown tool '{tool_name}'"

        # Send result back to Qwen
        tool_result_msg = {
            "role": "tool",
            "tool_call_id": call.id,
            "content": json.dumps({"result": result})
        }
        tool_msgs.append(tool_result_msg)

    # Step 3: Qwen final answer using tool output
    print(f"\n🤖 Getting final response from Qwen with tool results...")
    final_resp = client.chat.completions.create(
        model="qwen-4b",
        messages=tool_msgs
    )

    print("\nAssistant:", final_resp.choices[0].message.content)
else:
    # No tool call — just output Qwen's text
    print("\n💬 No tool calls detected - direct response from Qwen.")
    print("Assistant:", msg.content)
