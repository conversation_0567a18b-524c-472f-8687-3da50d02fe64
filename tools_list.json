[{"name": "add_numbers", "description": "Add two numbers together", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}, {"name": "subtract_numbers", "description": "Subtract second number from first", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}, {"name": "multiply_numbers", "description": "Multiply two numbers", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}, {"name": "divide_numbers", "description": "Divide first number by second", "parameters": {"type": "object", "properties": {"a": {"type": "number"}, "b": {"type": "number"}}, "required": ["a", "b"]}}, {"name": "square_number", "description": "Square a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}, {"name": "cube_number", "description": "Cube a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}, {"name": "square_root", "description": "Find square root of a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}, {"name": "cube_root", "description": "Find cube root of a number", "parameters": {"type": "object", "properties": {"a": {"type": "number"}}, "required": ["a"]}}]