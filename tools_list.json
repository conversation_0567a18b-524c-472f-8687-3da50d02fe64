[
    {
        "name": "add_numbers",
        "description": "Add two numbers together",
        "parameters": {
            "type": "object",
            "properties": {
                "a": {"type": "number"},
                "b": {"type": "number"}
            },
            "required": ["a", "b"]
        }
    },
    {
        "name": "subtract_numbers",
        "description": "Subtract second number from first",
        "parameters": {
            "type": "object",
            "properties": {
                "a": {"type": "number"},
                "b": {"type": "number"}
            },
            "required": ["a", "b"]
        }
    },
    {
        "name": "multiply_numbers",
        "description": "Multiply two numbers",
        "parameters": {
            "type": "object",
            "properties": {
                "a": {"type": "number"},
                "b": {"type": "number"}
            },
            "required": ["a", "b"]
        }
    },
    {
        "name": "divide_numbers",
        "description": "Divide first number by second",
        "parameters": {
            "type": "object",
            "properties": {
                "a": {"type": "number"},
                "b": {"type": "number"}
            },
            "required": ["a", "b"]
        }
    },
    {
        "name": "square_number",
        "description": "Square a number",
        "parameters": {
            "type": "object",
            "properties": {
                "a": {"type": "number"}
            },
            "required": ["a"]
        }
    },
    {
        "name": "cube_number",
        "description": "Cube a number",
        "parameters": {
            "type": "object",
            "properties": {
                "a": {"type": "number"}
            },
            "required": ["a"]
        }
    },
    {
        "name": "square_root",
        "description": "Find square root of a number",
        "parameters": {
            "type": "object",
            "properties": {
                "a": {"type": "number"}
            },
            "required": ["a"]
        }
    },
    {
        "name": "cube_root",
        "description": "Find cube root of a number",
        "parameters": {
            "type": "object",
            "properties": {
                "a": {"type": "number"}
            },
            "required": ["a"]
        }
    }
]

 Map names → function
TOOL_FUNCTIONS = {
    "add_numbers": add_numbers,
    "subtract_numbers": subtract_numbers,
    "multiply_numbers": multiply_numbers,
    "divide_numbers": divide_numbers,
    "square_number": square_number,
    "cube_number": cube_number,
    "square_root": square_root,
    "cube_root": cube_root
}
